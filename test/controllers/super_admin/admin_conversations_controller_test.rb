require 'test_helper'

class SuperAdmin::AdminConversationsControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    # Create admin users
    @superadmin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: '<PERSON>',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Support',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'ReadOnly',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @scout_user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Scout',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @scout_user, role: @scout_role)

    @talent_user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Talent',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @talent_user, role: @talent_role)

    # Create organization
    @organization = Organization.create!(name: 'Test Conversation Organization')
    [
      @superadmin,
      @support_admin,
      @readonly_admin,
      @scout_user,
      @talent_user,
    ].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end

    # Create test conversation
    @conversation =
      Conversation.find_or_create_by_participants(@scout_user, @talent_user)

    # Create test messages
    @message1 =
      Message.create!(
        conversation: @conversation,
        user: @scout_user,
        body: 'Hello from scout',
      )

    @message2 =
      Message.create!(
        conversation: @conversation,
        user: @talent_user,
        body: 'Hello from talent',
      )
  end

  test 'superadmin can access conversations index' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path
    assert_response :success
    assert_select 'h1', text: 'Conversations'
  end

  test 'support admin can access conversations index' do
    sign_in_as(@support_admin)
    get super_admin_admin_conversations_path
    assert_response :success
    assert_select 'h1', text: 'Conversations'
  end

  test 'readonly admin can access conversations index' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_conversations_path
    assert_response :success
    assert_select 'h1', text: 'Conversations'
  end

  test 'scout user cannot access conversations index' do
    sign_in_as(@scout_user)
    get super_admin_admin_conversations_path
    assert_redirected_to root_path
  end

  test 'unauthenticated user cannot access conversations index' do
    get super_admin_admin_conversations_path
    assert_redirected_to sign_in_path
  end

  test 'superadmin can view conversation details' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversation_path(@conversation)
    assert_response :success
    assert_select 'h1', text: /Conversation ##{@conversation.id}/
  end

  test 'conversations index displays search and filter options' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path
    assert_response :success

    assert_select 'input[name="search"]'
    assert_select 'button', text: 'Search'
    assert_select 'button', text: 'Clear'
  end

  test 'conversations index search functionality' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path,
        params: {
          search: @scout_user.email,
        }
    assert_response :success
    # Should find conversation with scout user as participant
  end

  test 'conversations index displays empty state when no conversations' do
    Conversation.destroy_all
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path
    assert_response :success
    assert_select '.empty-state', text: /No conversations found/
  end

  test 'conversations index pagination works' do
    # Create more conversations to test pagination
    25.times do |i|
      talent =
        User.create!(
          email: "talent_conv#{i}@test.com",
          password: 'password123123',
          verified: true,
          first_name: "Talent#{i}",
          last_name: 'User',
          onboarding_completed: true,
          onboarding_step: 'completed',
        )
      UserRole.create!(user: talent, role: @talent_role)

      Conversation.create!(participants: [@scout_user, talent])
    end

    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path
    assert_response :success
    assert_select '.pagination'
  end

  test 'export functionality for superadmin' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality for support admin' do
    sign_in_as(@support_admin)
    get super_admin_admin_conversations_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality for readonly admin' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_conversations_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'conversation show page displays all relevant information' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversation_path(@conversation)
    assert_response :success

    assert_select 'dt', text: 'Participants'
    assert_select 'dt', text: 'Messages Count'
    assert_select 'dd', text: '2'
    assert_select 'dt', text: 'Created At'
    assert_select 'dt', text: 'Updated At'
  end

  test 'conversation show page displays participant information' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversation_path(@conversation)
    assert_response :success

    # Should show both participants (either as links or text)
    assert_match @scout_user.full_name, response.body
    assert_match @talent_user.full_name, response.body
  end

  test 'conversation show page displays recent messages' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversation_path(@conversation)
    assert_response :success

    # Should show message content somewhere on the page
    assert_match @message1.body, response.body
    assert_match @message2.body, response.body
  end

  test 'conversations index shows message counts' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path
    assert_response :success

    assert_select 'td', text: '2' # Message count
  end

  test 'conversations index shows participant names' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path
    assert_response :success

    # Should show participant names in some form
    assert_select 'td', text: /#{@scout_user.first_name}/
    assert_select 'td', text: /#{@talent_user.first_name}/
  end

  test 'date range filtering works' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path,
        params: {
          created_from: 1.day.ago.strftime('%Y-%m-%d'),
          created_to: 1.day.from_now.strftime('%Y-%m-%d'),
        }
    assert_response :success
  end

  test 'conversations index shows last activity' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversations_path
    assert_response :success

    assert_select 'th', text: 'Last Activity'
  end

  test 'conversation show page has link to messages admin' do
    sign_in_as(@superadmin)
    get super_admin_admin_conversation_path(@conversation)
    assert_response :success

    assert_select 'a[href*="admin_messages"]', text: /View All Messages/
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'password123123' }
    follow_redirect!
  end
end
