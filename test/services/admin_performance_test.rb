# frozen_string_literal: true

require 'test_helper'

class AdminPerformanceTest < ActiveSupport::TestCase
  # Don't use fixtures to avoid foreign key issues
  self.use_transactional_tests = true

  # Override fixture loading
  def self.fixture_path
    nil
  end
  def setup
    # Clear caches before each test
    Rails.cache.clear
    AdminDashboardStatsService.clear_cache
    AdminRecentActivityService.clear_cache
    AdminFilterOptionsService.clear_all_caches
  end

  test 'dashboard stats service uses counter caches efficiently' do
    # Create test data without fixtures
    organization = Organization.create!(name: 'Test Org')
    user =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Test',
        last_name: 'User',
        verified: true,
      )

    job =
      Job.create!(
        title: 'Test Job',
        description: 'Test Description',
        organization: organization,
        job_category: :social_media,
        platform: :x_twitter,
        budget_range: :under_1000,
        work_duration: :short_term,
        outcome: :followers,
        status: :published,
      )

    # Create some test records
    3.times do |i|
      JobApplication.create!(
        job: job,
        user: user,
        status: :applied,
        application_letter: "Test application #{i}",
      )
    end

    conversation = Conversation.create!
    2.times do |i|
      Message.create!(
        conversation: conversation,
        user: user,
        body: "Test message #{i}",
      )
    end

    # Test that counter caches are working
    assert_equal 3, job.reload.job_applications_count
    assert_equal 2, conversation.reload.messages_count

    # Test dashboard stats generation
    stats = AdminDashboardStatsService.generate_stats

    assert stats[:applications][:total] >= 3
    assert stats[:communication][:messages] >= 2
    assert stats[:organizations][:total_jobs] >= 1
  end

  test 'filter options service caches efficiently' do
    # Test job filter options caching
    options1 = AdminFilterOptionsService.job_filter_options
    options2 = AdminFilterOptionsService.job_filter_options

    # Should return the same object (cached)
    assert_equal options1.object_id, options2.object_id

    # Test cache clearing
    AdminFilterOptionsService.clear_cache('job')
    options3 = AdminFilterOptionsService.job_filter_options

    # Should be a new object after cache clear
    assert_not_equal options1.object_id, options3.object_id
  end

  test 'recent activity service includes proper associations' do
    # Create test data
    user = users(:one)
    job = jobs(:one)

    # Create a job application
    JobApplication.create!(
      job: job,
      user: user,
      status: :applied,
      application_letter: 'Test application',
    )

    # Create a chat request
    ChatRequest.create!(
      scout: user,
      talent: users(:two),
      status: :pending,
      requested_at: Time.current,
    )

    activity = AdminRecentActivityService.generate_recent_activity

    assert activity.is_a?(Hash)
    assert activity.key?(:recent_applications)
    assert activity.key?(:recent_chat_requests)
  end

  test 'performance monitoring tracks query counts' do
    query_count = 0

    # Subscribe to SQL notifications to count queries
    subscription =
      ActiveSupport::Notifications.subscribe('sql.active_record') do |*args|
        query_count += 1
      end

    begin
      AdminPerformanceMonitorService.monitor_query('Test Query') do
        User.first
        Job.first
      end

      # Should have executed at least 2 queries
      assert query_count >= 2
    ensure
      ActiveSupport::Notifications.unsubscribe(subscription)
    end
  end

  test 'counter caches are maintained correctly' do
    organization = organizations(:one)
    job = jobs(:one)
    user = users(:one)

    initial_job_count = organization.jobs_count
    initial_app_count = job.job_applications_count

    # Create a new job
    new_job =
      Job.create!(
        title: 'Test Job',
        description: 'Test Description',
        organization: organization,
        job_category: :social_media,
        platform: :x_twitter,
        budget_range: :under_1000,
        work_duration: :short_term,
        outcome: :followers,
        status: :published,
      )

    # Counter cache should be updated
    assert_equal initial_job_count + 1, organization.reload.jobs_count

    # Create a job application
    JobApplication.create!(
      job: new_job,
      user: user,
      status: :applied,
      application_letter: 'Test application',
    )

    # Counter cache should be updated
    assert_equal 1, new_job.reload.job_applications_count
  end

  test 'cache invalidation works correctly' do
    # Generate initial stats (should cache)
    stats1 = AdminDashboardStatsService.generate_stats

    # Create new data
    organization = organizations(:one)
    Job.create!(
      title: 'Test Job for Cache',
      description: 'Test Description',
      organization: organization,
      job_category: :social_media,
      platform: :x_twitter,
      budget_range: :under_1000,
      work_duration: :short_term,
      outcome: :followers,
      status: :published,
    )

    # Stats should still be cached (same values)
    stats2 = AdminDashboardStatsService.generate_stats
    assert_equal stats1[:jobs][:total], stats2[:jobs][:total]

    # Clear cache manually
    AdminDashboardStatsService.clear_cache

    # Now stats should be updated
    stats3 = AdminDashboardStatsService.generate_stats
    assert stats3[:jobs][:total] > stats1[:jobs][:total]
  end
end
