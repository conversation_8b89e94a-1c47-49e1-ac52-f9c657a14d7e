# Searchkick configuration for different environments
if Rails.env.test?
  # In test environment, configure Searchkick to handle index conflicts
  # Use environment variables to configure Searchkick
  ENV['SEARCHKICK_INDEX_PREFIX'] = "test_#{Rails.env}"

  # Set shorter timeouts for tests
  ENV['SEARCHKICK_TIMEOUT'] = '5'
  ENV['SEARCHKICK_SEARCH_TIMEOUT'] = '3'
  
  # Add a test helper method to manually reindex when needed
  module SearchkickTestHelpers
    def reindex_all_searchkick_models
      return unless defined?(Searchkick)
      
      searchable_models = [
        TalentProfile,
        Job,
        JobApplication,
        Conversation
      ]
      
      searchable_models.each do |model|
        begin
          # Delete existing index to avoid alias conflicts
          model.searchkick_index.delete if model.searchkick_index.exists?
          # Create fresh index
          model.reindex
        rescue => e
          Rails.logger.warn "[SEARCHKICK] Failed to reindex #{model.name}: #{e.message}"
        end
      end
    end
    
    def clean_searchkick_indexes
      return unless defined?(Searchkick)
      
      searchable_models = [
        <PERSON><PERSON>rofile,
        Job,
        JobApplication,
        Conversation
      ]
      
      searchable_models.each do |model|
        begin
          model.searchkick_index.delete if model.searchkick_index.exists?
        rescue => e
          Rails.logger.warn "[<PERSON>AR<PERSON><PERSON><PERSON>K] Failed to clean index for #{model.name}: #{e.message}"
        end
      end
    end
  end
  
  # Include the helper in test classes
  ActiveSupport::TestCase.include SearchkickTestHelpers if defined?(ActiveSupport::TestCase)
  
elsif Rails.env.development?
  # Development environment configuration
  ENV['SEARCHKICK_INDEX_PREFIX'] = "dev_#{Rails.env}"
  ENV['SEARCHKICK_TIMEOUT'] = '10'
  ENV['SEARCHKICK_SEARCH_TIMEOUT'] = '5'

elsif Rails.env.production?
  # Production environment configuration
  ENV['SEARCHKICK_TIMEOUT'] = '15'
  ENV['SEARCHKICK_SEARCH_TIMEOUT'] = '10'
end

# Configure Searchkick client options to suppress warnings
if defined?(Searchkick)
  # Suppress Elasticsearch client verification warnings
  Searchkick.client_options = {
    transport_options: {
      ssl: { verify: false }
    },
    log: false
  }
end

Rails.logger.info "[SEARCHKICK] Configured for #{Rails.env} environment"
